package com.exam;

import com.exam.cache.core.CacheManager;
import com.exam.cache.datasource.DataSource;
import com.exam.cache.datasource.SimpleDataSource;
import com.exam.cache.policy.FIFOEvictionPolicy;
import com.exam.cache.policy.LFUEvictionPolicy;
import com.exam.cache.policy.LRUEvictionPolicy;
import com.exam.cache.stats.CacheStats;
import com.exam.model.Product;
import com.exam.model.User;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 缓存系统演示程序
 * 展示缓存系统的各项功能
 */
public class Main {

    public static void main(String[] args) throws Exception {
        System.out.println("=== 高性能缓存系统演示 ===\n");
        
        // 演示基本缓存操作
        demoBasicCacheOperations();
        
        // 演示缓存过期和自动刷新
        demoCacheExpirationAndRefresh();
        
        // 演示多级缓存和缓存穿透防护
        demoMultiLevelCacheAndCachePenetration();
        
        // 演示缓存淘汰策略
        demoEvictionPolicies();
        
        // 演示缓存统计和监控
        demoCacheStats();
        
        // 演示缓存雪崩防护
        demoCacheAvalancheProtection();
        
        // 演示批量操作
        demoBatchOperations();
        
        // 演示缓存同步机制
        demoCacheSynchronization();
    }

    /**
     * 演示基本缓存操作
     */
    private static void demoBasicCacheOperations() throws Exception {
        System.out.println("\n=== 测试用例1：基本缓存操作 ===\n");
        
        // 创建缓存管理器，使用LRU淘汰策略，容量为100
        CacheManager cacheManager = new CacheManager(new LRUEvictionPolicy<>(), 100);
        
        // 写入缓存
        User user = new User(1001, "张三");
        cacheManager.put("user:1001", user, Duration.ofMinutes(10));
        System.out.println("已将用户写入缓存: " + user);
        
        // 读取缓存
        Optional<User> cachedUser = cacheManager.get("user:1001", User.class);
        System.out.println("从缓存读取用户: " + (cachedUser.isPresent() ? cachedUser.get() : "未找到"));
        System.out.println("期望输出: user.isPresent() == true && user.get().getId() == 1001 && user.get().getName().equals(\"张三\")");
        System.out.println("实际结果: " + (cachedUser.isPresent() && cachedUser.get().getId() == 1001 && cachedUser.get().getName().equals("张三")));
        
        // 读取不存在的数据
        Optional<User> nonExistUser = cacheManager.get("user:9999", User.class);
        System.out.println("读取不存在的用户: " + (nonExistUser.isPresent() ? nonExistUser.get() : "未找到"));
        System.out.println("期望输出: nonExistUser.isPresent() == false");
        System.out.println("实际结果: " + (!nonExistUser.isPresent()));
        
        // 删除缓存
        cacheManager.remove("user:1001");
        cachedUser = cacheManager.get("user:1001", User.class);
        System.out.println("删除后再次读取用户: " + (cachedUser.isPresent() ? cachedUser.get() : "未找到"));
        System.out.println("期望输出: user.isPresent() == false");
        System.out.println("实际结果: " + (!cachedUser.isPresent()));
        
        // 关闭缓存管理器
        cacheManager.shutdown();
    }

    /**
     * 演示缓存过期和自动刷新
     */
    private static void demoCacheExpirationAndRefresh() throws Exception {
        System.out.println("\n=== 测试用例2：缓存过期和自动刷新 ===\n");
        
        // 创建缓存管理器，使用LRU淘汰策略，容量为100
        CacheManager cacheManager = new CacheManager(new LRUEvictionPolicy<>(), 100);
        
        // 写入短期缓存（2秒过期）
        Product product = new Product(2001, "笔记本电脑", 5999.00);
        cacheManager.put("product:2001", product, Duration.ofSeconds(2));
        System.out.println("已将产品写入缓存（2秒过期）: " + product);
        
        // 立即读取
        Optional<Product> cachedProduct = cacheManager.get("product:2001", Product.class);
        System.out.println("立即读取产品: " + (cachedProduct.isPresent() ? cachedProduct.get() : "未找到"));
        System.out.println("期望输出: product.isPresent() == true && product.get().getId() == 2001");
        System.out.println("实际结果: " + (cachedProduct.isPresent() && cachedProduct.get().getId() == 2001));
        
        // 等待缓存过期
        System.out.println("等待3秒，让缓存过期...");
        TimeUnit.SECONDS.sleep(3);
        
        // 再次读取
        cachedProduct = cacheManager.get("product:2001", Product.class);
        System.out.println("过期后再次读取产品: " + (cachedProduct.isPresent() ? cachedProduct.get() : "未找到"));
        System.out.println("期望输出: product.isPresent() == false");
        System.out.println("实际结果: " + (!cachedProduct.isPresent()));
        
        // 设置自动刷新的缓存
        Random random = new Random();
        cacheManager.putWithAutoRefresh("exchange:rate", () -> 6.3 + random.nextDouble() * 0.2, Duration.ofSeconds(2));
        System.out.println("已设置自动刷新的汇率缓存（2秒刷新一次）");
        
        // 多次读取，检查是否自动刷新
        double rate1 = cacheManager.get("exchange:rate", Double.class).orElse(0.0);
        System.out.println("第一次读取汇率: " + rate1);
        
        System.out.println("等待3秒，让缓存自动刷新...");
        TimeUnit.SECONDS.sleep(3);
        
        double rate2 = cacheManager.get("exchange:rate", Double.class).orElse(0.0);
        System.out.println("第二次读取汇率: " + rate2);
        
        System.out.println("期望输出: rate1 != 0.0 && rate2 != 0.0 && rate1 != rate2");
        System.out.println("实际结果: " + (rate1 != 0.0 && rate2 != 0.0 && rate1 != rate2));
        
        // 关闭缓存管理器
        cacheManager.shutdown();
    }

    /**
     * 演示多级缓存和缓存穿透防护
     */
    private static void demoMultiLevelCacheAndCachePenetration() throws Exception {
        System.out.println("\n=== 测试用例3：多级缓存和缓存穿透防护 ===\n");
        
        // 创建缓存管理器，使用LRU淘汰策略，容量为100
        CacheManager cacheManager = new CacheManager(new LRUEvictionPolicy<>(), 100);
        
        // 创建模拟数据源
        DataSource dataSource = new SimpleDataSource();
        
        // 向数据源中添加一些数据
        User user = new User(2001, "李四");
        dataSource.save("user:2001", user);
        System.out.println("已向数据源添加用户: " + user);
        
        // 设置数据源
        cacheManager.setDataSource(dataSource);
        System.out.println("已设置数据源");
        
        // 读取不在缓存但在数据源中的数据
        Optional<User> loadedUser = cacheManager.get("user:2001", User.class);
        System.out.println("从数据源加载用户: " + (loadedUser.isPresent() ? loadedUser.get() : "未找到"));
        System.out.println("期望输出: user.isPresent() == true && user.get().getId() == 2001 && user.get().getName().equals(\"李四\")");
        System.out.println("实际结果: " + (loadedUser.isPresent() && loadedUser.get().getId() == 2001 && loadedUser.get().getName().equals("李四")));
        
        // 再次读取，应该从缓存中获取
        CacheStats statsBefore = cacheManager.getStats();
        loadedUser = cacheManager.get("user:2001", User.class);
        CacheStats statsAfter = cacheManager.getStats();
        
        System.out.println("再次读取用户（应从缓存获取）: " + (loadedUser.isPresent() ? loadedUser.get() : "未找到"));
        System.out.println("期望输出: user.isPresent() == true && cacheManager.getStats().getHitCount() > 0");
        System.out.println("实际结果: " + (loadedUser.isPresent() && statsAfter.getHitCount() > statsBefore.getHitCount()));
        
        // 测试缓存穿透防护 - 多次请求不存在的数据
        System.out.println("测试缓存穿透防护 - 多次请求不存在的数据");
        for (int i = 0; i < 10; i++) {
            cacheManager.get("user:9999", User.class);
        }
        
        // 检查是否有空值缓存（防穿透）
        boolean hasNullCache = cacheManager.hasNullCache("user:9999");
        System.out.println("检查是否有空值缓存: " + hasNullCache);
        System.out.println("期望输出: hasNullCache == true");
        System.out.println("实际结果: " + hasNullCache);
        
        // 关闭缓存管理器
        cacheManager.shutdown();
    }

    /**
     * 演示缓存淘汰策略
     */
    private static void demoEvictionPolicies() throws Exception {
        System.out.println("\n=== 测试用例4：缓存淘汰策略 ===\n");
        
        // 创建缓存管理器，使用LRU淘汰策略，容量为3
        CacheManager cacheManager = new CacheManager(new LRUEvictionPolicy<>(), 3);
        System.out.println("创建LRU缓存管理器，容量为3");
        
        // 添加3个缓存项
        cacheManager.put("key1", "value1", Duration.ofMinutes(10));
        cacheManager.put("key2", "value2", Duration.ofMinutes(10));
        cacheManager.put("key3", "value3", Duration.ofMinutes(10));
        System.out.println("已添加3个缓存项: key1, key2, key3");
        
        // 访问key1，使其成为最近使用的
        cacheManager.get("key1", String.class);
        System.out.println("访问key1，使其成为最近使用的");
        
        // 添加第4个缓存项，应该淘汰key2（最久未使用的）
        cacheManager.put("key4", "value4", Duration.ofMinutes(10));
        System.out.println("添加第4个缓存项: key4，应该淘汰key2（最久未使用的）");
        
        // 检查key2是否被淘汰
        Optional<String> value2 = cacheManager.get("key2", String.class);
        System.out.println("检查key2是否被淘汰: " + (value2.isPresent() ? value2.get() : "已淘汰"));
        System.out.println("期望输出: value2.isPresent() == false");
        System.out.println("实际结果: " + (!value2.isPresent()));
        
        // 检查其他key是否存在
        Optional<String> value1 = cacheManager.get("key1", String.class);
        Optional<String> value3 = cacheManager.get("key3", String.class);
        Optional<String> value4 = cacheManager.get("key4", String.class);
        
        System.out.println("检查其他key是否存在:");
        System.out.println("key1: " + (value1.isPresent() ? value1.get() : "已淘汰"));
        System.out.println("key3: " + (value3.isPresent() ? value3.get() : "已淘汰"));
        System.out.println("key4: " + (value4.isPresent() ? value4.get() : "已淘汰"));
        
        System.out.println("期望输出: value1.isPresent() == true && value3.isPresent() == true && value4.isPresent() == true");
        System.out.println("实际结果: " + (value1.isPresent() && value3.isPresent() && value4.isPresent()));
        
        // 关闭缓存管理器
        cacheManager.shutdown();
        
        // 演示其他淘汰策略
        System.out.println("\n演示其他淘汰策略:");
        
        // 演示LFU淘汰策略
        CacheManager lfuCacheManager = new CacheManager(new LFUEvictionPolicy<>(), 3);
        System.out.println("创建LFU缓存管理器，容量为3");
        
        // 演示FIFO淘汰策略
        CacheManager fifoCacheManager = new CacheManager(new FIFOEvictionPolicy<>(), 3);
        System.out.println("创建FIFO缓存管理器，容量为3");
        
        // 关闭缓存管理器
        lfuCacheManager.shutdown();
        fifoCacheManager.shutdown();
    }
    
    /**
     * 演示缓存统计和监控
     */
    private static void demoCacheStats() throws Exception {
        System.out.println("\n=== 测试用例5：缓存统计和监控 ===\n");
        
        // 创建缓存管理器，使用LRU淘汰策略，容量为100
        CacheManager cacheManager = new CacheManager(new LRUEvictionPolicy<>(), 100);
        
        // 执行一系列缓存操作
        cacheManager.put("stat:key1", "value1", Duration.ofMinutes(10));
        cacheManager.get("stat:key1", String.class); // 命中
        cacheManager.get("stat:key2", String.class); // 未命中
        
        // 获取统计信息
        CacheStats stats = cacheManager.getStats();
        System.out.println("缓存统计信息: " + stats);
        
        System.out.println("期望输出: stats.getRequestCount() == 2 && stats.getHitCount() == 1 && stats.getMissCount() == 1");
        System.out.println("实际结果: " + (stats.getRequestCount() == 2 && stats.getHitCount() == 1 && stats.getMissCount() == 1));
        
        System.out.println("期望输出: Math.abs(stats.getHitRate() - 0.5) < 0.001");
        System.out.println("实际结果: " + (Math.abs(stats.getHitRate() - 0.5) < 0.001));
        
        // 测试监控功能 - 获取当前缓存大小
        int cacheSize = cacheManager.size();
        System.out.println("当前缓存大小: " + cacheSize);
        System.out.println("期望输出: cacheSize == 1");
        System.out.println("实际结果: " + (cacheSize == 1));
        
        // 关闭缓存管理器
        cacheManager.shutdown();
    }
    
    /**
     * 演示缓存雪崩防护
     */
    private static void demoCacheAvalancheProtection() throws Exception {
        System.out.println("\n=== 测试用例6：缓存雪崩防护 ===\n");
        
        // 创建缓存管理器，使用LRU淘汰策略，容量为100
        CacheManager cacheManager = new CacheManager(new LRUEvictionPolicy<>(), 100);
        
        // 模拟数据源
        DataSource dataSource = new SimpleDataSource();
        cacheManager.setDataSource(dataSource);
        
        System.out.println("测试缓存雪崩防护 - 使用随机过期时间");
        
        // 添加多个缓存项，它们会有略微不同的过期时间
        Duration baseTtl = Duration.ofSeconds(5);
        for (int i = 1; i <= 10; i++) {
            cacheManager.put("avalanche:key" + i, "value" + i, baseTtl);
        }
        
        // 检查随机过期时间
        System.out.println("等待3秒，然后检查缓存项的过期情况...");
        TimeUnit.SECONDS.sleep(3);
        
        // 检查所有缓存项的状态
        int expiredCount = 0;
        for (int i = 1; i <= 10; i++) {
            Optional<String> value = cacheManager.get("avalanche:key" + i, String.class);
            System.out.println("key" + i + ": " + (value.isPresent() ? "仍然有效" : "已过期"));
            if (!value.isPresent()) {
                expiredCount++;
            }
        }
        
        System.out.println("已过期的缓存项数量: " + expiredCount + "/10");
        System.out.println("由于随机过期时间，应该只有部分缓存项过期，而不是全部同时过期");
        
        // 等待所有缓存项过期
        System.out.println("\n等待4秒，所有缓存项应该都过期...");
        TimeUnit.SECONDS.sleep(4);
        
        // 再次检查所有缓存项的状态
        expiredCount = 0;
        for (int i = 1; i <= 10; i++) {
            Optional<String> value = cacheManager.get("avalanche:key" + i, String.class);
            if (!value.isPresent()) {
                expiredCount++;
            }
        }
        
        System.out.println("已过期的缓存项数量: " + expiredCount + "/10");
        System.out.println("期望输出: expiredCount == 10");
        System.out.println("实际结果: " + (expiredCount == 10));
        
        // 关闭缓存管理器
        cacheManager.shutdown();
    }
    
    /**
     * 演示批量操作
     */
    private static void demoBatchOperations() throws Exception {
        System.out.println("\n=== 测试用例7：批量操作 ===\n");
        
        // 创建缓存管理器，使用LRU淘汰策略，容量为100
        CacheManager cacheManager = new CacheManager(new LRUEvictionPolicy<>(), 100);
        
        // 准备批量数据
        Map<String, Object> batchData = new HashMap<>();
        for (int i = 1; i <= 5; i++) {
            batchData.put("batch:key" + i, "value" + i);
        }
        
        // 批量存入缓存
        System.out.println("批量存入缓存数据: " + batchData.keySet());
        cacheManager.multiPut(batchData, Duration.ofMinutes(10));
        
        // 批量获取缓存
        System.out.println("批量获取缓存数据");
        Map<String, String> results = cacheManager.multiGet(batchData.keySet(), String.class);
        
        // 检查结果
        System.out.println("获取到的数据: " + results);
        System.out.println("期望输出: results.size() == 5");
        System.out.println("实际结果: " + (results.size() == 5));
        
        // 测试异步获取
        System.out.println("\n测试异步获取");
        cacheManager.getAsync("batch:key1", String.class, result -> {
            System.out.println("异步获取结果: " + result.orElse("(未找到)"));
        });
        
        // 等待异步操作完成
        TimeUnit.SECONDS.sleep(1);
        
        // 关闭缓存管理器
        cacheManager.shutdown();
    }
    
    /**
     * 演示缓存同步机制
     */
    private static void demoCacheSynchronization() throws Exception {
        System.out.println("\n=== 测试用例8：缓存同步机制 ===\n");
        
        // 创建两个缓存管理器，模拟两个节点
        CacheManager node1 = new CacheManager(new LRUEvictionPolicy<>(), 100);
        CacheManager node2 = new CacheManager(new LRUEvictionPolicy<>(), 100);
        
        // 设置节点间的监听关系，实现缓存同步
        node1.addUpdateListener(new CacheManager.CacheUpdateListener() {
            @Override
            public void onCacheUpdate(String key, Object value, Duration ttl) {
                System.out.println("Node1 更新了缓存，同步到 Node2: " + key);
                node2.put(key, value, ttl);
            }
            
            @Override
            public void onCacheRemove(String key) {
                System.out.println("Node1 删除了缓存，同步到 Node2: " + key);
                node2.remove(key);
            }
        });
        
        // 在节点1上写入缓存
        System.out.println("在Node1上写入缓存");
        node1.put("sync:key1", "value1", Duration.ofMinutes(10));
        
        // 检查节点2是否自动同步了缓存
        TimeUnit.MILLISECONDS.sleep(100); // 等待同步完成
        Optional<String> node2Value = node2.get("sync:key1", String.class);
        
        System.out.println("从 Node2 上读取同步的缓存: " + node2Value.orElse("(未找到)"));
        System.out.println("期望输出: node2Value.isPresent() == true");
        System.out.println("实际结果: " + node2Value.isPresent());
        
        // 在节点1上删除缓存
        System.out.println("\n在Node1上删除缓存");
        node1.remove("sync:key1");
        
        // 检查节点2是否自动同步了删除操作
        TimeUnit.MILLISECONDS.sleep(100); // 等待同步完成
        node2Value = node2.get("sync:key1", String.class);
        
        System.out.println("删除后从 Node2 上读取: " + node2Value.orElse("(未找到)"));
        System.out.println("期望输出: node2Value.isPresent() == false");
        System.out.println("实际结果: " + !node2Value.isPresent());
        
        // 启动缓存同步任务
        System.out.println("\n启动缓存同步任务");
        node1.startCacheSync(Duration.ofSeconds(1));
        
        // 等待同步任务执行
        TimeUnit.SECONDS.sleep(2);
        
        // 关闭缓存管理器
        node1.shutdown();
        node2.shutdown();
    }
}
