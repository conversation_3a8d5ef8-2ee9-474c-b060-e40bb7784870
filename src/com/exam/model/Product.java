package com.exam.model;

import java.io.Serializable;

/**
 * 产品模型类，用于缓存示例
 */
public class Product implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private final int id;
    private final String name;
    private final double price;
    
    /**
     * 创建一个产品
     * 
     * @param id 产品ID
     * @param name 产品名称
     * @param price 产品价格
     */
    public Product(int id, String name, double price) {
        this.id = id;
        this.name = name;
        this.price = price;
    }
    
    /**
     * 获取产品ID
     * 
     * @return 产品ID
     */
    public int getId() {
        return id;
    }
    
    /**
     * 获取产品名称
     * 
     * @return 产品名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取产品价格
     * 
     * @return 产品价格
     */
    public double getPrice() {
        return price;
    }
    
    @Override
    public String toString() {
        return "Product{id=" + id + ", name='" + name + "', price=" + price + "}";
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Product product = (Product) o;
        
        if (id != product.id) return false;
        if (Double.compare(product.price, price) != 0) return false;
        return name != null ? name.equals(product.name) : product.name == null;
    }
    
    @Override
    public int hashCode() {
        int result;
        long temp;
        result = id;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        temp = Double.doubleToLongBits(price);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        return result;
    }
}
