package com.exam.model;

import java.io.Serializable;

/**
 * 用户模型类，用于缓存示例
 */
public class User implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private final int id;
    private final String name;
    
    /**
     * 创建一个用户
     * 
     * @param id 用户ID
     * @param name 用户名
     */
    public User(int id, String name) {
        this.id = id;
        this.name = name;
    }
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public int getId() {
        return id;
    }
    
    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    public String getName() {
        return name;
    }
    
    @Override
    public String toString() {
        return "User{id=" + id + ", name='" + name + "'}";
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        User user = (User) o;
        
        if (id != user.id) return false;
        return name != null ? name.equals(user.name) : user.name == null;
    }
    
    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        return result;
    }
}
