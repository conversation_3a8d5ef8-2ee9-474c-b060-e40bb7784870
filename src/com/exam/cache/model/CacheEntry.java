package com.exam.cache.model;

/**
 * 缓存条目类，用于存储缓存的值和过期时间
 */
public class CacheEntry {
    private final Object value;
    private final long expireTime;

    /**
     * 创建一个缓存条目
     * 
     * @param value 缓存的值
     * @param expireTime 过期时间戳（毫秒），0表示永不过期
     */
    public CacheEntry(Object value, long expireTime) {
        this.value = value;
        this.expireTime = expireTime;
    }

    /**
     * 获取缓存的值
     * 
     * @return 缓存的值
     */
    public Object getValue() {
        return value;
    }

    /**
     * 获取过期时间
     * 
     * @return 过期时间戳（毫秒）
     */
    public long getExpireTime() {
        return expireTime;
    }

    /**
     * 判断缓存是否已过期
     * 
     * @return 如果缓存已过期则返回true，否则返回false
     */
    public boolean isExpired() {
        return expireTime > 0 && System.currentTimeMillis() > expireTime;
    }
}
