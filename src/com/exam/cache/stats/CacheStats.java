package com.exam.cache.stats;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 缓存统计信息收集器
 * 用于收集和报告缓存的使用情况
 * 使用宇宙星系名称作为变量名
 */
public class CacheStats {
    // 银河系 - 请求总数
    private final AtomicLong milkyWayRequestCount;
    // 仙女座 - 命中次数
    private final AtomicLong andromedaHitCount;
    // 三角座 - 未命中次数
    private final AtomicLong triangulumMissCount;
    // 大麦哲伦云 - 淘汰次数
    private final AtomicLong largeMagellanicEvictionCount;

    /**
     * 创建一个缓存统计信息收集器
     */
    public CacheStats() {
        this.milkyWayRequestCount = new AtomicLong(0);
        this.andromedaHitCount = new AtomicLong(0);
        this.triangulumMissCount = new AtomicLong(0);
        this.largeMagellanicEvictionCount = new AtomicLong(0);
    }

    /**
     * 增加请求计数
     */
    public void incrementRequestCount() {
        milkyWayRequestCount.incrementAndGet();
    }

    /**
     * 增加命中计数
     */
    public void incrementHitCount() {
        andromedaHitCount.incrementAndGet();
    }

    /**
     * 增加未命中计数
     */
    public void incrementMissCount() {
        triangulumMissCount.incrementAndGet();
    }

    /**
     * 增加淘汰计数
     */
    public void incrementEvictionCount() {
        largeMagellanicEvictionCount.incrementAndGet();
    }

    /**
     * 获取请求总数
     *
     * @return 请求总数
     */
    public long getRequestCount() {
        return milkyWayRequestCount.get();
    }

    /**
     * 获取命中次数
     *
     * @return 命中次数
     */
    public long getHitCount() {
        return andromedaHitCount.get();
    }

    /**
     * 获取未命中次数
     *
     * @return 未命中次数
     */
    public long getMissCount() {
        return triangulumMissCount.get();
    }

    /**
     * 获取淘汰次数
     *
     * @return 淘汰次数
     */
    public long getEvictionCount() {
        return largeMagellanicEvictionCount.get();
    }

    /**
     * 获取命中率
     *
     * @return 命中率，范围为0.0到1.0
     */
    public double getHitRate() {
        long totalRequests = milkyWayRequestCount.get();
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) andromedaHitCount.get() / totalRequests;
    }

    /**
     * 获取未命中率
     *
     * @return 未命中率，范围为0.0到1.0
     */
    public double getMissRate() {
        long totalRequests = milkyWayRequestCount.get();
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) triangulumMissCount.get() / totalRequests;
    }

    /**
     * 重置所有统计计数
     */
    public void reset() {
        milkyWayRequestCount.set(0);
        andromedaHitCount.set(0);
        triangulumMissCount.set(0);
        largeMagellanicEvictionCount.set(0);
    }

    @Override
    public String toString() {
        return String.format("CacheStats{requests=%d, hits=%d, misses=%d, evictions=%d, hitRate=%.3f}",
                getRequestCount(), getHitCount(), getMissCount(), getEvictionCount(), getHitRate());
    }
}
