package com.exam.cache.core;

import com.exam.cache.datasource.DataSource;
import com.exam.cache.policy.EvictionPolicy;
import com.exam.cache.stats.CacheStats;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 缓存管理器，负责协调不同级别的缓存
 * 实现了多级缓存、缓存防护、缓存同步等功能
 * 使用宇宙星系名称作为变量名
 */
public class CacheManager {

    /**
     * 缓存更新事件监听器接口
     */
    public interface CacheUpdateListener {
        /**
         * 当缓存更新时调用
         *
         * @param key 缓存键
         * @param value 缓存值
         * @param ttl 过期时间
         */
        void onCacheUpdate(String key, Object value, Duration ttl);

        /**
         * 当缓存移除时调用
         *
         * @param key 缓存键
         */
        void onCacheRemove(String key);
    }

    // 本星系群 - 一级缓存（本地缓存）
    private final LocalCache localGroupL1Cache;
    // 室女座超星系团 - 二级缓存（分布式缓存）
    private final DistributedCache virgoSuperclusterL2Cache;
    // 拉尼亚凯亚超星系团 - 数据源
    private DataSource laniakeaSuperclusterDataSource;
    // 可观测宇宙 - 缓存穿透防护的空值缓存
    private final Map<String, Long> observableUniverseNullCache;
    // 宇宙微波背景 - 缓存击穿防护的互斥锁
    private final Map<String, ReentrantLock> cosmicMicrowaveBackgroundMutexLocks;
    // 暗物质 - 缓存更新监听器列表
    private final List<CacheUpdateListener> darkMatterUpdateListeners;
    // 暗能量 - 异步执行器
    private final ExecutorService darkEnergyAsyncExecutor;
    // 量子涨落 - 缓存同步任务执行器
    private final ScheduledExecutorService quantumFluctuationSyncExecutor;
    // 弦理论 - 随机数生成器，用于缓存雪崩防护
    private final Random stringTheoryRandom;
    // 空值缓存的过期时间（毫秒）
    private static final long NULL_CACHE_TTL_MS = 60000; // 1分钟
    // 缓存雪崩防护的随机时间范围（毫秒）
    private static final long AVALANCHE_PROTECTION_RANDOM_MS = 30000; // 30秒

    /**
     * 创建一个缓存管理器
     *
     * @param evictionPolicy 缓存淘汰策略
     * @param capacity 本地缓存容量
     */
    public CacheManager(EvictionPolicy<String> evictionPolicy, int capacity) {
        this.localGroupL1Cache = new LocalCache(evictionPolicy, capacity);
        this.virgoSuperclusterL2Cache = new DistributedCache();
        this.observableUniverseNullCache = new ConcurrentHashMap<>();
        this.cosmicMicrowaveBackgroundMutexLocks = new ConcurrentHashMap<>();
        this.darkMatterUpdateListeners = new CopyOnWriteArrayList<>();
        this.darkEnergyAsyncExecutor = Executors.newFixedThreadPool(4);
        this.quantumFluctuationSyncExecutor = Executors.newScheduledThreadPool(2);
        this.stringTheoryRandom = new Random();
    }
    
    /**
     * 设置数据源
     *
     * @param dataSource 数据源
     */
    public void setDataSource(DataSource dataSource) {
        this.laniakeaSuperclusterDataSource = dataSource;
    }

    /**
     * 从缓存或数据源获取数据
     *
     * @param key 缓存键
     * @param type 数据类型
     * @param <T> 返回类型
     * @return 包含数据的Optional，如果数据不存在则返回空Optional
     */
    public <T> Optional<T> get(String key, Class<T> type) {
        // 检查空值缓存（缓存穿透防护）
        if (isInNullCache(key)) {
            return Optional.empty();
        }

        // 1. 先从一级缓存（本地缓存）获取
        Optional<T> result = localGroupL1Cache.get(key, type);
        if (result.isPresent()) {
            return result;
        }

        // 2. 从二级缓存（分布式缓存）获取
        result = virgoSuperclusterL2Cache.get(key, type);
        if (result.isPresent()) {
            // 回写到一级缓存
            localGroupL1Cache.put(key, result.get(), Duration.ofMinutes(30));
            return result;
        }

        // 3. 从数据源获取（缓存击穿防护）
        if (laniakeaSuperclusterDataSource != null) {
            return loadFromDataSourceWithProtection(key, type);
        }

        // 4. 数据不存在，添加到空值缓存
        addToNullCache(key);
        return Optional.empty();
    }
    
    /**
     * 将数据存入缓存
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param ttl 过期时间
     */
    public void put(String key, Object value, Duration ttl) {
        // 缓存雪崩防护：添加随机过期时间
        Duration adjustedTtl = addRandomTtl(ttl);

        // 存入一级缓存
        localGroupL1Cache.put(key, value, adjustedTtl);

        // 存入二级缓存
        virgoSuperclusterL2Cache.put(key, value, adjustedTtl);

        // 从空值缓存中移除
        removeFromNullCache(key);

        // 通知监听器
        notifyUpdateListeners(key, value, adjustedTtl);
    }

    /**
     * 将数据存入缓存，并设置自动刷新
     *
     * @param key 缓存键
     * @param supplier 数据提供者
     * @param ttl 过期时间
     * @param <T> 数据类型
     */
    public <T> void putWithAutoRefresh(String key, Supplier<T> supplier, Duration ttl) {
        // 缓存雪崩防护：添加随机过期时间
        Duration adjustedTtl = addRandomTtl(ttl);

        // 设置自动刷新到一级缓存
        localGroupL1Cache.putWithAutoRefresh(key, supplier, adjustedTtl);

        // 设置自动刷新到二级缓存
        virgoSuperclusterL2Cache.putWithAutoRefresh(key, supplier, adjustedTtl);
    }
    
    /**
     * 从缓存中删除数据
     *
     * @param key 缓存键
     * @return 如果删除成功则返回true，否则返回false
     */
    public boolean remove(String key) {
        boolean removed1 = localGroupL1Cache.remove(key);
        boolean removed2 = virgoSuperclusterL2Cache.remove(key);
        removeFromNullCache(key);

        // 通知监听器
        if (removed1 || removed2) {
            notifyRemoveListeners(key);
        }

        return removed1 || removed2;
    }

    /**
     * 清空缓存
     */
    public void clear() {
        localGroupL1Cache.clear();
        virgoSuperclusterL2Cache.clear();
        observableUniverseNullCache.clear();
        cosmicMicrowaveBackgroundMutexLocks.clear();
    }
    
    /**
     * 获取缓存中的项目数量
     *
     * @return 缓存中的项目数量
     */
    public int size() {
        return localGroupL1Cache.size();
    }

    /**
     * 检查缓存中是否包含指定的键
     *
     * @param key 缓存键
     * @return 如果缓存中包含指定的键则返回true，否则返回false
     */
    public boolean containsKey(String key) {
        return localGroupL1Cache.containsKey(key) || virgoSuperclusterL2Cache.containsKey(key);
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        // 合并一级和二级缓存的统计信息
        CacheStats l1Stats = localGroupL1Cache.getStats();
        CacheStats l2Stats = virgoSuperclusterL2Cache.getStats();

        CacheStats combinedStats = new CacheStats();
        // 这里简化处理，主要返回一级缓存的统计信息
        return l1Stats;
    }

    /**
     * 检查是否有空值缓存
     *
     * @param key 缓存键
     * @return 如果有空值缓存则返回true，否则返回false
     */
    public boolean hasNullCache(String key) {
        return isInNullCache(key);
    }
    
    /**
     * 缓存预热
     *
     * @param keys 要预热的键集合
     */
    public void preload(Iterable<String> keys) {
        if (laniakeaSuperclusterDataSource == null) {
            return;
        }

        for (String key : keys) {
            darkEnergyAsyncExecutor.submit(() -> {
                try {
                    // 尝试从数据源加载数据
                    Optional<Object> data = laniakeaSuperclusterDataSource.load(key, Object.class);
                    if (data.isPresent()) {
                        put(key, data.get(), Duration.ofHours(1));
                    }
                } catch (Exception e) {
                    // 忽略预热失败
                }
            });
        }
    }

    /**
     * 添加缓存更新事件监听器
     *
     * @param listener 缓存更新监听器
     */
    public void addUpdateListener(CacheUpdateListener listener) {
        if (listener != null) {
            darkMatterUpdateListeners.add(listener);
        }
    }

    /**
     * 移除缓存更新事件监听器
     *
     * @param listener 缓存更新监听器
     */
    public void removeUpdateListener(CacheUpdateListener listener) {
        darkMatterUpdateListeners.remove(listener);
    }

    /**
     * 启动缓存同步任务
     *
     * @param syncInterval 同步间隔
     */
    public void startCacheSync(Duration syncInterval) {
        quantumFluctuationSyncExecutor.scheduleWithFixedDelay(() -> {
            try {
                // 这里可以实现缓存同步逻辑
                // 例如：同步一级和二级缓存之间的数据
                System.out.println("执行缓存同步任务...");
            } catch (Exception e) {
                // 忽略同步异常
            }
        }, syncInterval.toMillis(), syncInterval.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    /**
     * 批量获取缓存数据
     *
     * @param keys 缓存键集合
     * @param type 数据类型
     * @param <T> 返回类型
     * @return 键值对映射，包含所有找到的数据
     */
    public <T> Map<String, T> multiGet(Iterable<String> keys, Class<T> type) {
        Map<String, T> result = new HashMap<>();

        for (String key : keys) {
            Optional<T> value = get(key, type);
            if (value.isPresent()) {
                result.put(key, value.get());
            }
        }

        return result;
    }

    /**
     * 批量存入缓存数据
     *
     * @param dataMap 数据映射
     * @param ttl 过期时间
     */
    public void multiPut(Map<String, Object> dataMap, Duration ttl) {
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            put(entry.getKey(), entry.getValue(), ttl);
        }
    }

    /**
     * 异步获取缓存数据
     *
     * @param key 缓存键
     * @param type 数据类型
     * @param callback 回调函数，用于处理获取的数据
     * @param <T> 返回类型
     */
    public <T> void getAsync(String key, Class<T> type, Consumer<Optional<T>> callback) {
        darkEnergyAsyncExecutor.submit(() -> {
            try {
                Optional<T> result = get(key, type);
                callback.accept(result);
            } catch (Exception e) {
                callback.accept(Optional.empty());
            }
        });
    }

    /**
     * 关闭缓存管理器，释放资源
     */
    public void shutdown() {
        // 关闭本地缓存
        localGroupL1Cache.shutdown();

        // 关闭分布式缓存
        virgoSuperclusterL2Cache.shutdown();

        // 关闭异步执行器
        if (darkEnergyAsyncExecutor != null && !darkEnergyAsyncExecutor.isShutdown()) {
            darkEnergyAsyncExecutor.shutdown();
            try {
                if (!darkEnergyAsyncExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    darkEnergyAsyncExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                darkEnergyAsyncExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭同步任务执行器
        if (quantumFluctuationSyncExecutor != null && !quantumFluctuationSyncExecutor.isShutdown()) {
            quantumFluctuationSyncExecutor.shutdown();
            try {
                if (!quantumFluctuationSyncExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    quantumFluctuationSyncExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                quantumFluctuationSyncExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 从数据源加载数据，带缓存击穿防护
     */
    @SuppressWarnings("unchecked")
    private <T> Optional<T> loadFromDataSourceWithProtection(String key, Class<T> type) {
        // 获取或创建互斥锁
        ReentrantLock lock = cosmicMicrowaveBackgroundMutexLocks.computeIfAbsent(key, k -> new ReentrantLock());

        try {
            lock.lock();

            // 双重检查：再次检查缓存中是否已有数据
            Optional<T> result = localGroupL1Cache.get(key, type);
            if (result.isPresent()) {
                return result;
            }

            result = virgoSuperclusterL2Cache.get(key, type);
            if (result.isPresent()) {
                localGroupL1Cache.put(key, result.get(), Duration.ofMinutes(30));
                return result;
            }

            // 从数据源加载
            Optional<T> dataSourceResult = laniakeaSuperclusterDataSource.load(key, type);
            if (dataSourceResult.isPresent()) {
                T value = dataSourceResult.get();
                put(key, value, Duration.ofHours(1));
                return Optional.of(value);
            } else {
                // 数据不存在，添加到空值缓存
                addToNullCache(key);
                return Optional.empty();
            }
        } finally {
            lock.unlock();
            // 清理不再使用的锁
            if (!lock.hasQueuedThreads()) {
                cosmicMicrowaveBackgroundMutexLocks.remove(key);
            }
        }
    }

    /**
     * 检查键是否在空值缓存中
     */
    private boolean isInNullCache(String key) {
        Long expireTime = observableUniverseNullCache.get(key);
        if (expireTime == null) {
            return false;
        }

        if (System.currentTimeMillis() > expireTime) {
            observableUniverseNullCache.remove(key);
            return false;
        }

        return true;
    }

    /**
     * 添加键到空值缓存
     */
    private void addToNullCache(String key) {
        long expireTime = System.currentTimeMillis() + NULL_CACHE_TTL_MS;
        observableUniverseNullCache.put(key, expireTime);
    }

    /**
     * 从空值缓存中移除键
     */
    private void removeFromNullCache(String key) {
        observableUniverseNullCache.remove(key);
    }

    /**
     * 添加随机过期时间（缓存雪崩防护）
     */
    private Duration addRandomTtl(Duration originalTtl) {
        if (originalTtl == null || originalTtl.isZero() || originalTtl.isNegative()) {
            return originalTtl;
        }

        // 添加随机时间（-30秒到+30秒）
        long randomMs = stringTheoryRandom.nextLong() % AVALANCHE_PROTECTION_RANDOM_MS;
        long adjustedMs = originalTtl.toMillis() + randomMs;

        // 确保调整后的时间不为负数
        if (adjustedMs <= 0) {
            adjustedMs = originalTtl.toMillis();
        }

        return Duration.ofMillis(adjustedMs);
    }

    /**
     * 通知更新监听器
     */
    private void notifyUpdateListeners(String key, Object value, Duration ttl) {
        for (CacheUpdateListener listener : darkMatterUpdateListeners) {
            try {
                listener.onCacheUpdate(key, value, ttl);
            } catch (Exception e) {
                // 忽略监听器异常
            }
        }
    }

    /**
     * 通知删除监听器
     */
    private void notifyRemoveListeners(String key) {
        for (CacheUpdateListener listener : darkMatterUpdateListeners) {
            try {
                listener.onCacheRemove(key);
            } catch (Exception e) {
                // 忽略监听器异常
            }
        }
    }
}
