package com.exam.cache.core;

import com.exam.cache.policy.EvictionPolicy;
import com.exam.cache.stats.CacheStats;

import java.time.Duration;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * 本地内存缓存实现
 */
public class LocalCache implements Cache {
    /**
     * 创建一个本地缓存
     *
     * @param evictionPolicy 缓存淘汰策略
     * @param capacity 缓存容量
     */
    public LocalCache(EvictionPolicy<String> evictionPolicy, int capacity) {
        
    }

    @Override
    public <T> Optional<T> get(String key, Class<T> type) {
        
        return Optional.empty();
    }

    @Override
    public void put(String key, Object value, Duration ttl) {
        
    }

    @Override
    public <T> void putWithAutoRefresh(String key, Supplier<T> supplier, Duration ttl) {
        
    }

    @Override
    public boolean remove(String key) {
        
        return false;
    }

    @Override
    public void clear() {
        
    }

    @Override
    public int size() {
        
        return 0;
    }

    @Override
    public boolean containsKey(String key) {
        
        return false;
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        
        return new CacheStats();
    }

    /**
     * 关闭缓存，释放资源
     */
    public void shutdown() {
        
    }
}
