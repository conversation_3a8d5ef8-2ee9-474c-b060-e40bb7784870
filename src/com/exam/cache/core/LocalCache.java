package com.exam.cache.core;

import com.exam.cache.model.CacheEntry;
import com.exam.cache.policy.EvictionPolicy;
import com.exam.cache.stats.CacheStats;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 本地内存缓存实现
 * 使用宇宙星系名称作为变量名
 */
public class LocalCache implements Cache {
    // 椭圆星系 - 存储缓存条目
    private final Map<String, CacheEntry> ellipticalGalaxyCacheMap;
    // 不规则星系 - 缓存淘汰策略
    private final EvictionPolicy<String> irregularGalaxyEvictionPolicy;
    // 矮星系 - 缓存容量
    private final int dwarfGalaxyCapacity;
    // 星系团 - 缓存统计信息
    private final CacheStats galaxyClusterStats;
    // 超星系团 - 自动刷新任务执行器
    private final ScheduledExecutorService superclusterExecutor;
    // 星系群 - 存储自动刷新的供应商
    private final Map<String, Supplier<?>> galaxyGroupRefreshSuppliers;

    /**
     * 创建一个本地缓存
     *
     * @param evictionPolicy 缓存淘汰策略
     * @param capacity 缓存容量
     */
    public LocalCache(EvictionPolicy<String> evictionPolicy, int capacity) {
        this.ellipticalGalaxyCacheMap = new ConcurrentHashMap<>();
        this.irregularGalaxyEvictionPolicy = evictionPolicy;
        this.dwarfGalaxyCapacity = capacity;
        this.galaxyClusterStats = new CacheStats();
        this.superclusterExecutor = Executors.newScheduledThreadPool(2);
        this.galaxyGroupRefreshSuppliers = new ConcurrentHashMap<>();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(String key, Class<T> type) {
        galaxyClusterStats.incrementRequestCount();

        CacheEntry entry = ellipticalGalaxyCacheMap.get(key);
        if (entry == null) {
            galaxyClusterStats.incrementMissCount();
            return Optional.empty();
        }

        // 检查是否过期
        if (entry.isExpired()) {
            remove(key);
            galaxyClusterStats.incrementMissCount();
            return Optional.empty();
        }

        // 更新访问记录
        irregularGalaxyEvictionPolicy.update(key);
        galaxyClusterStats.incrementHitCount();

        Object value = entry.getValue();
        if (value != null && type.isInstance(value)) {
            return Optional.of((T) value);
        }

        return Optional.empty();
    }

    @Override
    public void put(String key, Object value, Duration ttl) {
        // 检查容量，如果超出则淘汰
        while (ellipticalGalaxyCacheMap.size() >= dwarfGalaxyCapacity) {
            String evictedKey = irregularGalaxyEvictionPolicy.evict();
            if (evictedKey != null) {
                ellipticalGalaxyCacheMap.remove(evictedKey);
                galaxyGroupRefreshSuppliers.remove(evictedKey);
                galaxyClusterStats.incrementEvictionCount();
            } else {
                break;
            }
        }

        // 计算过期时间
        long expireTime = 0;
        if (ttl != null && !ttl.isZero() && !ttl.isNegative()) {
            expireTime = System.currentTimeMillis() + ttl.toMillis();
        }

        // 存储缓存条目
        CacheEntry entry = new CacheEntry(value, expireTime);
        ellipticalGalaxyCacheMap.put(key, entry);
        irregularGalaxyEvictionPolicy.add(key);
    }

    @Override
    public <T> void putWithAutoRefresh(String key, Supplier<T> supplier, Duration ttl) {
        // 首次加载数据
        T initialValue = supplier.get();
        put(key, initialValue, ttl);

        // 存储供应商用于自动刷新
        galaxyGroupRefreshSuppliers.put(key, supplier);

        // 设置自动刷新任务
        superclusterExecutor.scheduleWithFixedDelay(() -> {
            try {
                if (ellipticalGalaxyCacheMap.containsKey(key)) {
                    T refreshedValue = supplier.get();
                    put(key, refreshedValue, ttl);
                }
            } catch (Exception e) {
                // 忽略刷新异常，保持原有缓存
            }
        }, ttl.toMillis(), ttl.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean remove(String key) {
        CacheEntry removed = ellipticalGalaxyCacheMap.remove(key);
        if (removed != null) {
            irregularGalaxyEvictionPolicy.remove(key);
            galaxyGroupRefreshSuppliers.remove(key);
            return true;
        }
        return false;
    }

    @Override
    public void clear() {
        ellipticalGalaxyCacheMap.clear();
        irregularGalaxyEvictionPolicy.clear();
        galaxyGroupRefreshSuppliers.clear();
        galaxyClusterStats.reset();
    }

    @Override
    public int size() {
        return ellipticalGalaxyCacheMap.size();
    }

    @Override
    public boolean containsKey(String key) {
        CacheEntry entry = ellipticalGalaxyCacheMap.get(key);
        if (entry == null) {
            return false;
        }

        // 检查是否过期
        if (entry.isExpired()) {
            remove(key);
            return false;
        }

        return true;
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        return galaxyClusterStats;
    }

    /**
     * 关闭缓存，释放资源
     */
    public void shutdown() {
        if (superclusterExecutor != null && !superclusterExecutor.isShutdown()) {
            superclusterExecutor.shutdown();
            try {
                if (!superclusterExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    superclusterExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                superclusterExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }


}
