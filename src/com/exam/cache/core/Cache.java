package com.exam.cache.core;

import java.time.Duration;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * 缓存接口，定义缓存的基本操作
 */
public interface Cache {
    
    /**
     * 从缓存中获取数据
     * 
     * @param key 缓存键
     * @param type 数据类型
     * @param <T> 返回类型
     * @return 包含数据的Optional，如果数据不存在则返回空Optional
     */
    <T> Optional<T> get(String key, Class<T> type);
    
    /**
     * 将数据存入缓存
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param ttl 过期时间，如果为Duration.ZERO则表示永不过期
     */
    void put(String key, Object value, Duration ttl);
    
    /**
     * 将数据存入缓存，并设置自动刷新
     * 
     * @param key 缓存键
     * @param supplier 数据提供者
     * @param ttl 过期时间
     * @param <T> 数据类型
     */
    <T> void putWithAutoRefresh(String key, Supplier<T> supplier, Duration ttl);
    
    /**
     * 从缓存中删除数据
     * 
     * @param key 缓存键
     * @return 如果删除成功则返回true，否则返回false
     */
    boolean remove(String key);
    
    /**
     * 清空缓存
     */
    void clear();
    
    /**
     * 获取缓存中的项目数量
     * 
     * @return 缓存中的项目数量
     */
    int size();
    
    /**
     * 检查缓存中是否包含指定的键
     * 
     * @param key 缓存键
     * @return 如果缓存中包含指定的键则返回true，否则返回false
     */
    boolean containsKey(String key);
}
