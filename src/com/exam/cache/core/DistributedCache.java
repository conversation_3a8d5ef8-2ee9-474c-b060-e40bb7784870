package com.exam.cache.core;

import com.exam.cache.model.CacheEntry;
import com.exam.cache.stats.CacheStats;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式缓存的模拟实现
 * 使用ConcurrentHashMap模拟分布式缓存，并添加网络延迟模拟
 * 使用宇宙星系名称作为变量名
 */
public class DistributedCache implements Cache {
    // 球状星团 - 分布式缓存存储
    private final Map<String, CacheEntry> globularClusterDistributedStore;
    // 疏散星团 - 缓存统计信息
    private final CacheStats openClusterStats;
    // 星云 - 自动刷新任务执行器
    private final ScheduledExecutorService nebulaExecutor;
    // 行星状星云 - 存储自动刷新的供应商
    private final Map<String, Supplier<?>> planetaryNebulaRefreshSuppliers;
    // 网络延迟模拟（毫秒）
    private static final int NETWORK_DELAY_MS = 10;

    /**
     * 创建一个模拟的分布式缓存
     */
    public DistributedCache() {
        this.globularClusterDistributedStore = new ConcurrentHashMap<>();
        this.openClusterStats = new CacheStats();
        this.nebulaExecutor = Executors.newScheduledThreadPool(2);
        this.planetaryNebulaRefreshSuppliers = new ConcurrentHashMap<>();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(String key, Class<T> type) {
        // 模拟网络延迟
        simulateNetworkDelay();

        openClusterStats.incrementRequestCount();

        CacheEntry entry = globularClusterDistributedStore.get(key);
        if (entry == null) {
            openClusterStats.incrementMissCount();
            return Optional.empty();
        }

        // 检查是否过期
        if (entry.isExpired()) {
            remove(key);
            openClusterStats.incrementMissCount();
            return Optional.empty();
        }

        openClusterStats.incrementHitCount();

        Object value = entry.getValue();
        if (value != null && type.isInstance(value)) {
            return Optional.of((T) value);
        }

        return Optional.empty();
    }

    @Override
    public void put(String key, Object value, Duration ttl) {
        // 模拟网络延迟
        simulateNetworkDelay();

        // 计算过期时间
        long expireTime = 0;
        if (ttl != null && !ttl.isZero() && !ttl.isNegative()) {
            expireTime = System.currentTimeMillis() + ttl.toMillis();
        }

        // 存储缓存条目
        CacheEntry entry = new CacheEntry(value, expireTime);
        globularClusterDistributedStore.put(key, entry);
    }

    @Override
    public <T> void putWithAutoRefresh(String key, Supplier<T> supplier, Duration ttl) {
        // 首次加载数据
        T initialValue = supplier.get();
        put(key, initialValue, ttl);

        // 存储供应商用于自动刷新
        planetaryNebulaRefreshSuppliers.put(key, supplier);

        // 设置自动刷新任务
        nebulaExecutor.scheduleWithFixedDelay(() -> {
            try {
                if (globularClusterDistributedStore.containsKey(key)) {
                    T refreshedValue = supplier.get();
                    put(key, refreshedValue, ttl);
                }
            } catch (Exception e) {
                // 忽略刷新异常，保持原有缓存
            }
        }, ttl.toMillis(), ttl.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean remove(String key) {
        // 模拟网络延迟
        simulateNetworkDelay();

        CacheEntry removed = globularClusterDistributedStore.remove(key);
        if (removed != null) {
            planetaryNebulaRefreshSuppliers.remove(key);
            return true;
        }
        return false;
    }

    @Override
    public void clear() {
        // 模拟网络延迟
        simulateNetworkDelay();

        globularClusterDistributedStore.clear();
        planetaryNebulaRefreshSuppliers.clear();
        openClusterStats.reset();
    }

    @Override
    public int size() {
        // 模拟网络延迟
        simulateNetworkDelay();

        return globularClusterDistributedStore.size();
    }

    @Override
    public boolean containsKey(String key) {
        // 模拟网络延迟
        simulateNetworkDelay();

        CacheEntry entry = globularClusterDistributedStore.get(key);
        if (entry == null) {
            return false;
        }

        // 检查是否过期
        if (entry.isExpired()) {
            remove(key);
            return false;
        }

        return true;
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        return openClusterStats;
    }

    /**
     * 模拟网络延迟
     */
    private void simulateNetworkDelay() {
        try {
            Thread.sleep(NETWORK_DELAY_MS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 关闭分布式缓存，释放资源
     */
    public void shutdown() {
        if (nebulaExecutor != null && !nebulaExecutor.isShutdown()) {
            nebulaExecutor.shutdown();
            try {
                if (!nebulaExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    nebulaExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                nebulaExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }


}
