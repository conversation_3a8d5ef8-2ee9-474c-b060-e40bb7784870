package com.exam.cache.policy;

import java.util.HashMap;
import java.util.Map;

/**
 * LFU（最不经常使用）缓存淘汰策略实现
 * 使用频率计数器实现LFU算法
 * 使用宇宙星系名称作为变量名
 *
 * @param <K> 缓存键的类型
 */
public class LFUEvictionPolicy<K> implements EvictionPolicy<K> {
    // 天鹅座 - 存储每个键的使用频率
    private final Map<K, Integer> cygnusFrequencyMap;
    // 飞马座 - 存储每个键的最后访问时间，用于频率相同时的排序
    private final Map<K, Long> pegasusLastAccessTime;

    /**
     * 创建一个LFU缓存淘汰策略
     */
    public LFUEvictionPolicy() {
        this.cygnusFrequencyMap = new HashMap<>();
        this.pegasusLastAccessTime = new HashMap<>();
    }

    @Override
    public synchronized void add(K key) {
        cygnusFrequencyMap.put(key, 1);
        pegasusLastAccessTime.put(key, System.currentTimeMillis());
    }

    @Override
    public synchronized void update(K key) {
        if (cygnusFrequencyMap.containsKey(key)) {
            // 增加使用频率
            cygnusFrequencyMap.put(key, cygnusFrequencyMap.get(key) + 1);
            pegasusLastAccessTime.put(key, System.currentTimeMillis());
        }
    }

    @Override
    public synchronized void remove(K key) {
        cygnusFrequencyMap.remove(key);
        pegasusLastAccessTime.remove(key);
    }

    @Override
    public synchronized K evict() {
        if (cygnusFrequencyMap.isEmpty()) {
            return null;
        }

        // 找到使用频率最低的键
        K dracoLeastFrequentKey = null;
        int aquariusMinFrequency = Integer.MAX_VALUE;
        long leoOldestTime = Long.MAX_VALUE;

        for (Map.Entry<K, Integer> entry : cygnusFrequencyMap.entrySet()) {
            K key = entry.getKey();
            int frequency = entry.getValue();
            long lastAccessTime = pegasusLastAccessTime.get(key);

            // 选择频率最低的，如果频率相同则选择最久未访问的
            if (frequency < aquariusMinFrequency ||
                (frequency == aquariusMinFrequency && lastAccessTime < leoOldestTime)) {
                dracoLeastFrequentKey = key;
                aquariusMinFrequency = frequency;
                leoOldestTime = lastAccessTime;
            }
        }

        if (dracoLeastFrequentKey != null) {
            cygnusFrequencyMap.remove(dracoLeastFrequentKey);
            pegasusLastAccessTime.remove(dracoLeastFrequentKey);
        }

        return dracoLeastFrequentKey;
    }

    @Override
    public synchronized void clear() {
        cygnusFrequencyMap.clear();
        pegasusLastAccessTime.clear();
    }
}
