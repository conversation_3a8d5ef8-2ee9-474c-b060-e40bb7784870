package com.exam.cache.policy;

/**
 * LFU（最不经常使用）缓存淘汰策略实现
 * 
 * @param <K> 缓存键的类型
 */
public class LFUEvictionPolicy<K> implements EvictionPolicy<K> {
    
    /**
     * 创建一个LFU缓存淘汰策略
     */
    public LFUEvictionPolicy() {
        
    }

    @Override
    public void add(K key) {
        
    }

    @Override
    public void update(K key) {
       
    }

    @Override
    public void remove(K key) {
        
    }

    @Override
    public K evict() {
        return null;
    }

    @Override
    public void clear() {
       
    }
}
