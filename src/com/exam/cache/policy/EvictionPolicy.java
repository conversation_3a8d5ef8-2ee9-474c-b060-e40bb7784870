package com.exam.cache.policy;

/**
 * 缓存淘汰策略接口
 * 
 * @param <K> 缓存键的类型
 */
public interface EvictionPolicy<K> {
    
    /**
     * 添加一个键到策略中
     * 
     * @param key 要添加的键
     */
    void add(K key);
    
    /**
     * 更新一个键的使用状态
     * 
     * @param key 要更新的键
     */
    void update(K key);
    
    /**
     * 从策略中移除一个键
     * 
     * @param key 要移除的键
     */
    void remove(K key);
    
    /**
     * 根据策略选择一个要淘汰的键
     * 
     * @return 要淘汰的键，如果没有可淘汰的键则返回null
     */
    K evict();
    
    /**
     * 清空策略中的所有键
     */
    void clear();
}
