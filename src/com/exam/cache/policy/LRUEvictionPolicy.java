package com.exam.cache.policy;

/**
 * LRU（最近最少使用）缓存淘汰策略实现
 * 
 * @param <K> 缓存键的类型
 */
public class LRUEvictionPolicy<K> implements EvictionPolicy<K> {

    /**
     * 创建一个LRU缓存淘汰策略
     */
    public LRUEvictionPolicy() {
        
    }

    @Override
    public void add(K key) {
        
    }

    @Override
    public void update(K key) {
        
    }

    @Override
    public void remove(K key) {
        
    }

    @Override
    public K evict() {
        return null;
    }

    @Override
    public void clear() {
        
    }
}
