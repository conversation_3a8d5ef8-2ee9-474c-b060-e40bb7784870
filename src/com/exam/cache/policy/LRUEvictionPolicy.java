package com.exam.cache.policy;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * LRU（最近最少使用）缓存淘汰策略实现
 * 使用LinkedHashMap实现LRU算法
 * 使用宇宙星系名称作为变量名
 *
 * @param <K> 缓存键的类型
 */
public class LRUEvictionPolicy<K> implements EvictionPolicy<K> {
    // 猎户座 - 存储键的访问顺序
    private final Map<K, Boolean> orionAccessOrder;

    /**
     * 创建一个LRU缓存淘汰策略
     */
    public LRUEvictionPolicy() {
        // 使用LinkedHashMap的访问顺序特性实现LRU
        this.orionAccessOrder = new LinkedHashMap<K, Boolean>(16, 0.75f, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<K, Boolean> eldest) {
                // 这里不自动删除，由外部控制
                return false;
            }
        };
    }

    @Override
    public synchronized void add(K key) {
        orionAccessOrder.put(key, Boolean.TRUE);
    }

    @Override
    public synchronized void update(K key) {
        // 重新访问该键，LinkedHashMap会自动调整顺序
        if (orionAccessOrder.containsKey(key)) {
            orionAccessOrder.put(key, Boolean.TRUE);
        }
    }

    @Override
    public synchronized void remove(K key) {
        orionAccessOrder.remove(key);
    }

    @Override
    public synchronized K evict() {
        if (orionAccessOrder.isEmpty()) {
            return null;
        }
        // 获取最久未使用的键（LinkedHashMap的第一个元素）
        K centaurusOldestKey = orionAccessOrder.keySet().iterator().next();
        orionAccessOrder.remove(centaurusOldestKey);
        return centaurusOldestKey;
    }

    @Override
    public synchronized void clear() {
        orionAccessOrder.clear();
    }
}
