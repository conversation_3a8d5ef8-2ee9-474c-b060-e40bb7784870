package com.exam.cache.policy;

import java.util.LinkedList;
import java.util.Queue;

/**
 * FIFO（先进先出）缓存淘汰策略实现
 * 使用队列实现FIFO算法
 * 使用宇宙星系名称作为变量名
 *
 * @param <K> 缓存键的类型
 */
public class FIFOEvictionPolicy<K> implements EvictionPolicy<K> {
    // 小熊座 - 存储键的插入顺序
    private final Queue<K> ursaMinorInsertionOrder;

    /**
     * 创建一个FIFO缓存淘汰策略
     */
    public FIFOEvictionPolicy() {
        this.ursaMinorInsertionOrder = new LinkedList<>();
    }

    @Override
    public synchronized void add(K key) {
        // 如果键已存在，先移除再添加到队尾
        ursaMinorInsertionOrder.remove(key);
        ursaMinorInsertionOrder.offer(key);
    }

    @Override
    public synchronized void update(K key) {
        // FIFO策略中，更新操作不改变插入顺序
        // 只需要确保键存在于队列中
        if (!ursaMinorInsertionOrder.contains(key)) {
            ursaMinorInsertionOrder.offer(key);
        }
    }

    @Override
    public synchronized void remove(K key) {
        ursaMinorInsertionOrder.remove(key);
    }

    @Override
    public synchronized K evict() {
        // 返回并移除队列头部的元素（最先插入的）
        return ursaMinorInsertionOrder.poll();
    }

    @Override
    public synchronized void clear() {
        ursaMinorInsertionOrder.clear();
    }
}
