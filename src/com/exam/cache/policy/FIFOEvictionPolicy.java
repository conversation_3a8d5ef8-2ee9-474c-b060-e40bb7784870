package com.exam.cache.policy;

/**
 * FIFO（先进先出）缓存淘汰策略实现
 * 
 * @param <K> 缓存键的类型
 */
public class FIFOEvictionPolicy<K> implements EvictionPolicy<K> {
   
    /**
     * 创建一个FIFO缓存淘汰策略
     */
    public FIFOEvictionPolicy() {

    }

    @Override
    public void add(K key) {
       
    }

    @Override
    public void update(K key) {
        
    }

    @Override
    public void remove(K key) {
        
    }

    @Override
    public K evict() {
        return null;
    }

    @Override
    public void clear() {
        
    }
}
