package com.exam.cache.datasource;

import java.util.Optional;

/**
 * 数据源接口，定义从底层数据存储获取数据的方法
 */
public interface DataSource {
    
    /**
     * 从数据源获取数据
     * 
     * @param key 数据键
     * @param type 数据类型
     * @param <T> 返回类型
     * @return 包含数据的Optional，如果数据不存在则返回空Optional
     */
    <T> Optional<T> load(String key, Class<T> type);
    
    /**
     * 将数据保存到数据源
     * 
     * @param key 数据键
     * @param value 数据值
     * @return 如果保存成功则返回true，否则返回false
     */
    boolean save(String key, Object value);
    
    /**
     * 从数据源删除数据
     * 
     * @param key 数据键
     * @return 如果删除成功则返回true，否则返回false
     */
    boolean delete(String key);
    
    /**
     * 检查数据源中是否存在指定的键
     * 
     * @param key 数据键
     * @return 如果数据源中存在指定的键则返回true，否则返回false
     */
    boolean exists(String key);
}
