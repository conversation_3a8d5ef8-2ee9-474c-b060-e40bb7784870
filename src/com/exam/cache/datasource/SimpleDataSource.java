package com.exam.cache.datasource;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简单数据源实现，使用内存存储模拟数据库操作
 * 使用宇宙星系名称作为变量名
 */
public class SimpleDataSource implements DataSource {
    // 螺旋星系 - 存储数据的内存映射
    private final Map<String, Object> spiralGalaxyDataStore;

    /**
     * 创建一个简单数据源
     */
    public SimpleDataSource() {
        this.spiralGalaxyDataStore = new ConcurrentHashMap<>();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> load(String key, Class<T> type) {
        Object value = spiralGalaxyDataStore.get(key);
        if (value != null && type.isInstance(value)) {
            return Optional.of((T) value);
        }
        return Optional.empty();
    }

    @Override
    public boolean save(String key, Object value) {
        if (key == null || value == null) {
            return false;
        }
        spiralGalaxyDataStore.put(key, value);
        return true;
    }

    @Override
    public boolean delete(String key) {
        if (key == null) {
            return false;
        }
        return spiralGalaxyDataStore.remove(key) != null;
    }

    @Override
    public boolean exists(String key) {
        return key != null && spiralGalaxyDataStore.containsKey(key);
    }
}
