package com.exam.cache.datasource;

import java.util.Map;
import java.util.Optional;

/**
 * 简单数据源实现，使用内存存储模拟数据库操作
 */
public class SimpleDataSource implements DataSource {
    
    /**
     * 创建一个简单数据源
     */
    public SimpleDataSource() {
    }
    
    @Override
    public <T> Optional<T> load(String key, Class<T> type) {
        return Optional.empty();
    }

    @Override
    public boolean save(String key, Object value) {
        return false;
    }

    @Override
    public boolean delete(String key) {
        return false;
    }

    @Override
    public boolean exists(String key) {
        return false;
    }
}
